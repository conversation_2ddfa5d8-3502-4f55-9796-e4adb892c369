{"name": "@melgeek/lowcode-materials", "private": true, "version": "0.0.0", "description": "Materials for Melgeek LowCode Editor", "scripts": {"prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "f2elint-scan": "f2elint scan", "f2elint-fix": "f2elint fix", "update-readme": "node scripts/update-readme.js", "prepublishOnly": "npm run build && npm run lowcode:build && npm run lowcode:antd:build"}, "lint-staged": {"*.ts?(x)": ["prettier --parser=typescript --write"], "*.{js,jsx,less,md,json}": ["prettier --write"]}, "devDependencies": {"f2elint": "^2.2.0", "glob": "^11.0.0", "lint-staged": "^10.5.3", "prettier": "^1.19.1", "typescript": "^3.9.3", "yorkie": "^2.0.0"}, "authors": [{"name": "<PERSON>"}], "license": "MIT", "husky": {"hooks": {"pre-commit": "f2elint commit-file-scan", "commit-msg": "f2elint commit-msg-scan"}}}