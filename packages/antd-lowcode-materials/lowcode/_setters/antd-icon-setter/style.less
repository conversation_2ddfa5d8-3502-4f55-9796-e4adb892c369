.lc-antd-icon-setter {
  &-header {
    &-style {
      width: 100%;
      margin-bottom: 12px;
      display: flex;

      > label {
        flex: 1;
        text-align: center;
      }
    }

    &-search {
      width: 100%;
      margin-bottom: 12px;
      padding-bottom: 12px;
      border-bottom: 1px solid #ddd;
    }
  }

  &-list {
    display: block;
    max-height: 400px;
    overflow-y: auto;
    overflow-x: hidden;

    &-item {
      display: inline-block;
      position: relative;
      width: 36px;
      height: 36px;
      line-height: 36px;
      text-align: center;
      font-size: 24px;
      border-radius: 4px;
      transition: background-color 0.2s;

      &-name {
        display: none;
        font-size: 14px;
        position: absolute;
        height: 30px;
        word-break: keep-all;
        white-space: nowrap;
        bottom: -24px;
        left: 50%;
        z-index: 100;
        height: 24px;
        line-height: 24px;
        padding: 0 5px;
        border-radius: 4px;
        background-color: #fff;
        pointer-events: none;
        transform: translateX(-50%);
        box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
          0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
      }

      &:hover {
        background-color: #f1f2f3;

        .lc-antd-icon-setter-list-item-name {
          display: block;
        }
      }
    }
  }
}
