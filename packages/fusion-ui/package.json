{"name": "@alifd/fusion-ui", "version": "2.1.0", "description": "A component library based on Fusion Next", "files": ["es/", "lib/", "build/", "dist/", "lowcode/", "lowcode_lib/", "lowcode_es/"], "main": "lib/index.js", "module": "es/index.js", "lowcodeEditMain": "build/lowcode/view.js", "stylePath": "style.js", "scripts": {"start": "npm run dumi", "build": "build-scripts build", "lowcode:dev": "build-scripts start --config ./build.lowcode.js", "lowcode:build": "build-scripts build --config ./build.lowcode.js", "test": "build-scripts test", "lint": "f2elint scan", "lint:fix": "f2elint fix", "dumi": "cross-env APP_ROOT=docs dumi dev", "dumi:build": "cross-env APP_ROOT=docs dumi build", "prepublishOnly": "npm run build && npm run lowcode:build && npm run dumi:build"}, "keywords": ["react", "component"], "dependencies": {"@alifd/layout": "^2.0.7", "@alifd/next": "^1.25.18", "@antv/data-set": "^0.11.8", "@sindresorhus/is": "^4.0.1", "ahooks": "^2.10.11", "bizcharts": "^4.1.16", "classnames": "^2.3.1", "framer-motion": "^4.1.17", "lodash": "^4.17.21", "moment": "^2.29.4", "numeral": "^2.0.6", "numvert": "^0.2.2", "prop-types": "^15.5.8", "qs": "^6.10.1", "rc-resize-observer": "^1.0.0", "react-beautiful-dnd": "^13.1.0", "react-sortablejs": "6.1.1", "sortablejs": "^1.14.0", "@babel/runtime": "^7.0.0"}, "devDependencies": {"@alifd/theme-2": "^0.4.4", "node-sass": "^8.0.0", "@umijs/plugin-sass": "^1.1.1", "@alilc/lowcode-engine": "^1.0.0", "@alib/build-scripts": "^0.1.3", "@alifd/build-plugin-lowcode": "^0.4.0", "@types/lodash": "^4.14.177", "@types/react": "^16.14.15", "@types/react-dom": "^16.9.4", "build-plugin-component": "^1.12.0", "build-plugin-fusion": "^0.1.0", "build-plugin-load-assets": "^0.1.3", "build-plugin-moment-locales": "^0.1.0", "cross-env": "^7.0.3", "dumi": "^1.1.49", "enzyme": "^3.10.0", "enzyme-adapter-react-16": "^1.14.0", "eslint": "^6.0.1", "f2elint": "^1.2.0", "fs-extra": "^10.0.0", "marked": "^2.1.3", "prop-types": "^15.7.2", "remark-codesandbox": "^0.10.0", "reqwest": "^2.0.5", "to-string-loader": "^1.1.6"}, "peerDependencies": {"@alifd/next": "1.x", "react": "^16.9.0", "react-dom": "^16.9.0"}, "resolutions": {"webpack": "4.x"}, "license": "MIT", "homepage": "https://unpkg.com/@alifd/fusion-ui@2.1.0/build/index.html", "componentConfig": {"name": "FusionUI", "category": "精品物料库", "isComponentLibrary": true, "materialSchema": "https://alifd.alicdn.com/npm/@alifd/fusion-ui@2.1.0/build/lowcode/assets-prod.json"}, "husky": {"hooks": {"pre-commit": "f2elint commit-file-scan", "commit-msg": "f2elint commit-msg-scan"}}, "materialConfig": {"fusion-site": {"id": 1, "name": "PC 官网", "url": "https://fusion.design/api/v1/sites/1/materials"}}, "repository": "https://github.com/alibaba/lowcode-materials.git", "exports": {"./prototype": {"require": "./lowcode_lib/meta.js", "import": "./lowcode_es/meta.js"}, "./prototypeView": {"require": "./lowcode_lib/view.js", "import": "./lowcode_es/view.js"}, "./*": "./*", ".": {"import": "./es/index.js", "require": "./lib/index.js"}}, "lcMeta": {"type": "component"}}