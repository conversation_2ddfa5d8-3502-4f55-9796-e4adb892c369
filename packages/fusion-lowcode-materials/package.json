{"name": "@alilc/lowcode-materials", "version": "1.2.1", "description": "Fusion Next for LowCode", "main": "lib/index.js", "module": "es/index.js", "lowcodeEditMain": "build/lowcode/view.js", "stylePath": "style.js", "scripts": {"start": "build-scripts start", "build": "build-scripts build", "lowcode:dev": "build-scripts start --config ./build.lowcode.js", "lowcode:build": "build-scripts build --config ./build.lowcode.js", "prepublishOnly": "npm run build && npm run lowcode:build", "beta": "npm publish --tag beta", "pub": "npm publish"}, "files": ["es/", "lib/", "dist/", "build/", "lowcode/", "lowcode_lib/", "lowcode_es/"], "resolutions": {"webpack": "4.x"}, "dependencies": {"@alifd/next": "^1.24.14", "@types/react": "^16.14.0", "big.js": "^6.2.1", "lodash": "^4.17.21", "moment": "^2.29.4", "@babel/runtime": "^7.0.0"}, "devDependencies": {"@alib/build-scripts": "^0.1.23", "@alifd/build-plugin-lowcode": "^0.4.1", "@alifd/theme-2": "^0.4.4", "@types/big.js": "^6.1.6", "@types/lodash": "^4.14.191", "build-plugin-component": "^1.3.3", "build-plugin-fusion": "^0.1.3", "build-plugin-moment-locales": "^0.1.0", "moment": "^2.20.1", "react": "^16.14.0", "react-dom": "^16.14.0", "typescript": "^3.9.3"}, "authors": [{"name": "金禅"}, {"name": "荣彬"}, {"name": "屹凡"}, {"name": "启剑"}, {"name": "春希"}, {"name": "度城"}, {"name": "梧忌"}], "componentConfig": {"materialSchema": "https://alifd.alicdn.com/npm/@alilc/lowcode-materials@1.2.1/build/lowcode/assets-prod.json"}, "license": "MIT", "homepage": "https://unpkg.com/@alilc/lowcode-materials@1.2.1/build/index.html", "exports": {".": {"import": "./es/index.js", "require": "./lib/index.js"}, "./prototype": {"require": "./lowcode_lib/meta.js", "import": "./lowcode_es/meta.js"}, "./prototypeView": {"require": "./lowcode_lib/view.js", "import": "./lowcode_es/view.js"}, "./*": "./*"}, "lcMeta": {"type": "component"}, "repository": "https://github.com/alibaba/lowcode-materials.git"}