{"compileOnSave": false, "buildOnSave": false, "compilerOptions": {"allowSyntheticDefaultImports": true, "esModuleInterop": true, "outDir": "build", "module": "esnext", "target": "es6", "jsx": "react", "moduleResolution": "node", "lib": ["es6", "dom"], "sourceMap": true, "allowJs": true, "noUnusedLocals": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": true, "skipLibCheck": true, "paths": {"@/*": ["./src/*"]}, "baseUrl": "./"}, "include": ["src/*.ts", "src/*.tsx", "src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "build", "public"]}