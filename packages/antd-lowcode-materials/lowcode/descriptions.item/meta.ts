export default {
  componentName: 'Descriptions.Item',
  group: 'MelGeek组件',
  title: '描述列表项',
  props: [
    {
      name: 'key',
      title: {
        label: 'key',
        tip: 'key',
      },
      propType: 'string',
    },
    {
      name: 'tab',
      title: {
        label: '标题',
        tip: '标题',
      },
      propType: 'string',
    },
  ],
  configure: {
    component: { isContainer: true },
    supports: { style: true },
  },
};
