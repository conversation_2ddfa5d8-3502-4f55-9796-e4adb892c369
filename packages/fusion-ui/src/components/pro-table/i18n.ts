export default {
  'zh-CN': {
    columnTitleFilterPanelOk: '确认',
    columnTitleFilterPanelReset: '重置',
    columnTitleFilterPanelSearchPlaceholder: '搜索',
    columnTitleFilterPanelMenuSearchPlaceholder: '搜索',
    columnTitleFilterPanelAsc: '升序',
    columnTitleFilterPanelDesc: '倒序',

    selectionTooltipChosen: '已选择',
    selectionTooltipChosenEnd: '项',
    operation: '操作',
    indexColumnTitle: '序号',

    fullscreenButtonOpenTooltip: '全屏',
    fullscreenButtonCloseTooltip: '取消全屏',
    compactButtonCompactTooltip: '紧凑模式',
    compactButtonNormalTooltip: '正常模式',
    zebraButtonOpenTooltip: '斑马线',
    zebraButtonCloseTooltip: '隐藏斑马线',
    settingButtonTooltip: '列展示',

    settingButtonLockLeft: '固定在表格左侧',
    settingButtonLockRight: '固定在表格右侧',
    settingButtonLockCancel: '取消固定',

    // 分页
    pagiationTotal: '共',
    pagiationItem: '项',
    pagiationItems: '项',
  },
  'en-US': {
    columnTitleFilterPanelOk: 'Ok',

    selectionTooltipChosen: '',
    selectionTooltipChosenEnd: 'items selected',
    operation: 'Action',
    indexColumnTitle: 'Index',

    fullscreenButtonOpenTooltip: 'Fullscreen',
    compactButtonBalloonText: 'Density Adjustment',
    zebraButtonOpenTooltip: 'Zebra',

    // 分页
    pagiationTotal: 'Total',
    pagiationItem: 'item',
    pagiationItems: 'items',
  },
};
